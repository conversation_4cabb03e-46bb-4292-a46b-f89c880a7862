"""
Background Scheduler for IPO Data Collection
Handles periodic data collection with offline resilience and recovery.
"""

import time
import signal
import sys
from datetime import datetime, timedelta
from threading import Event
from loguru import logger
from data_collectors import IPODataManager
from database import get_db_session
from models import DataSource
from config import Config

class IPOScheduler:
    """Manages scheduled IPO data collection with offline handling"""
    
    def __init__(self):
        self.data_manager = IPODataManager()
        self.running = False
        self.stop_event = Event()
        self.check_interval = Config.SCHEDULER_INTERVAL_HOURS * 3600  # Convert to seconds
        self.offline_retry_interval = 300  # 5 minutes when offline
        self.max_offline_retries = 12  # Max retries before giving up (1 hour)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
    
    def start(self):
        """Start the background scheduler"""
        if self.running:
            logger.warning("Scheduler is already running")
            return
        
        self.running = True
        logger.info(f"Starting IPO scheduler (check every {Config.SCHEDULER_INTERVAL_HOURS} hours)")
        
        # Run initial collection
        self._run_collection_cycle()
        
        # Main scheduling loop
        while self.running and not self.stop_event.is_set():
            try:
                # Wait for the next scheduled run
                if self.stop_event.wait(timeout=self.check_interval):
                    break  # Stop event was set
                
                if self.running:
                    self._run_collection_cycle()
                    
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                # Continue running even if there's an error
                time.sleep(60)  # Wait a minute before retrying
        
        logger.info("IPO scheduler stopped")
    
    def stop(self):
        """Stop the scheduler gracefully"""
        logger.info("Stopping IPO scheduler...")
        self.running = False
        self.stop_event.set()
    
    def _run_collection_cycle(self):
        """Run a single data collection cycle with offline handling"""
        logger.info("Starting IPO data collection cycle")
        
        try:
            # Check if we're online
            if not self.data_manager.sec_collector.is_online():
                logger.warning("System appears to be offline, will retry later")
                self._handle_offline_scenario()
                return
            
            # Collect new IPOs
            new_count = self.data_manager.collect_new_ipos()
            
            # Log collection results
            self._log_collection_stats(new_count)
            
            # Update last successful run
            self._update_last_run_status(True)
            
        except Exception as e:
            logger.error(f"Collection cycle failed: {e}")
            self._update_last_run_status(False, str(e))
    
    def _handle_offline_scenario(self):
        """Handle offline scenarios with intelligent retry"""
        logger.info("Handling offline scenario")
        
        retry_count = 0
        while retry_count < self.max_offline_retries and self.running:
            logger.info(f"Offline retry {retry_count + 1}/{self.max_offline_retries}")
            
            # Wait before retrying
            if self.stop_event.wait(timeout=self.offline_retry_interval):
                return  # Stop event was set
            
            # Check if we're back online
            if self.data_manager.sec_collector.is_online():
                logger.info("Back online! Resuming normal operation")
                self._run_collection_cycle()
                return
            
            retry_count += 1
        
        logger.warning("Max offline retries reached, will try again on next scheduled run")
    
    def _log_collection_stats(self, new_count: int):
        """Log collection statistics"""
        try:
            with get_db_session() as session:
                from models import Company, IPO
                
                total_companies = session.query(Company).count()
                total_ipos = session.query(IPO).count()
                active_ipos = session.query(IPO).filter(IPO.is_active == True).count()
                
                logger.info(f"Collection complete: {new_count} new IPOs added")
                logger.info(f"Database stats: {total_companies} companies, {total_ipos} IPOs ({active_ipos} active)")
                
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
    
    def _update_last_run_status(self, success: bool, error_msg: str = None):
        """Update the last run status in database"""
        try:
            with get_db_session() as session:
                # Update scheduler status (we can use a special data source for this)
                scheduler_source = session.query(DataSource).filter(
                    DataSource.source_name == "SCHEDULER"
                ).first()
                
                if not scheduler_source:
                    scheduler_source = DataSource(source_name="SCHEDULER")
                    session.add(scheduler_source)
                
                scheduler_source.last_check = datetime.utcnow()
                if success:
                    scheduler_source.last_successful_update = datetime.utcnow()
                    scheduler_source.error_count = 0
                    scheduler_source.last_error = None
                else:
                    scheduler_source.error_count += 1
                    scheduler_source.last_error = error_msg
                
                session.commit()
                
        except Exception as e:
            logger.error(f"Failed to update scheduler status: {e}")
    
    def get_status(self) -> dict:
        """Get current scheduler status"""
        try:
            with get_db_session() as session:
                scheduler_source = session.query(DataSource).filter(
                    DataSource.source_name == "SCHEDULER"
                ).first()
                
                if not scheduler_source:
                    return {
                        'running': self.running,
                        'last_run': None,
                        'last_successful_run': None,
                        'error_count': 0,
                        'next_run': None
                    }
                
                next_run = None
                if scheduler_source.last_successful_update:
                    next_run = scheduler_source.last_successful_update + timedelta(hours=Config.SCHEDULER_INTERVAL_HOURS)
                
                return {
                    'running': self.running,
                    'last_run': scheduler_source.last_check,
                    'last_successful_run': scheduler_source.last_successful_update,
                    'error_count': scheduler_source.error_count,
                    'last_error': scheduler_source.last_error,
                    'next_run': next_run,
                    'check_interval_hours': Config.SCHEDULER_INTERVAL_HOURS
                }
                
        except Exception as e:
            logger.error(f"Failed to get scheduler status: {e}")
            return {'error': str(e)}

def run_scheduler():
    """Main function to run the scheduler"""
    logger.info("IPO Tracker Background Scheduler Starting...")
    
    try:
        scheduler = IPOScheduler()
        scheduler.start()
    except KeyboardInterrupt:
        logger.info("Scheduler interrupted by user")
    except Exception as e:
        logger.error(f"Scheduler crashed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_scheduler()
